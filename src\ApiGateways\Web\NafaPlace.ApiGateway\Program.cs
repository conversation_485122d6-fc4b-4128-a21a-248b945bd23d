var builder = WebApplication.CreateBuilder(args);

// Add health checks first
builder.Services.AddHealthChecks();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline - NO HTTPS redirect for Fly.io health checks
app.UseCors("AllowAll");

// Map health checks - simple and fast
app.MapHealthChecks("/health");

// Simple health check endpoint
app.MapGet("/", () => Results.Ok(new {
    status = "API Gateway is running",
    timestamp = DateTime.UtcNow,
    version = "1.0.0"
}));

// Additional health check endpoint with more details
app.MapGet("/health/detailed", () => Results.Ok(new {
    status = "Healthy",
    timestamp = DateTime.UtcNow,
    version = "1.0.0",
    environment = app.Environment.EnvironmentName
}))
   .WithName("DetailedHealthCheck")
   .WithTags("Health");

app.Run();
