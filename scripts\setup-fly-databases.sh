#!/bin/bash

# Script de création des bases de données PostgreSQL sur Fly.io
# Usage: ./setup-fly-databases.sh [staging|production]

ENVIRONMENT=${1:-staging}

if [ "$ENVIRONMENT" = "staging" ]; then
    DB_SUFFIX="-staging"
else
    DB_SUFFIX=""
fi

echo "🗄️ Création des bases de données pour l'environnement: $ENVIRONMENT"

# Base de données principale (Catalog)
echo "📦 Création de la base de données Catalog..."
flyctl postgres create \
  --name "nafaplace-catalog-db$DB_SUFFIX" \
  --region cdg \
  --vm-size shared-cpu-1x \
  --volume-size 10 \
  --initial-cluster-size 1

# Base de données Identity
echo "🔑 Création de la base de données Identity..."
flyctl postgres create \
  --name "nafaplace-identity-db$DB_SUFFIX" \
  --region cdg \
  --vm-size shared-cpu-1x \
  --volume-size 5 \
  --initial-cluster-size 1

# Base de données Order
echo "📋 Création de la base de données Order..."
flyctl postgres create \
  --name "nafaplace-order-db$DB_SUFFIX" \
  --region cdg \
  --vm-size shared-cpu-1x \
  --volume-size 10 \
  --initial-cluster-size 1

# Base de données Reviews
echo "⭐ Création de la base de données Reviews..."
flyctl postgres create \
  --name "nafaplace-reviews-db$DB_SUFFIX" \
  --region cdg \
  --vm-size shared-cpu-1x \
  --volume-size 5 \
  --initial-cluster-size 1

# Base de données Notifications
echo "🔔 Création de la base de données Notifications..."
flyctl postgres create \
  --name "nafaplace-notifications-db$DB_SUFFIX" \
  --region cdg \
  --vm-size shared-cpu-1x \
  --volume-size 5 \
  --initial-cluster-size 1

# Redis pour le cache (Cart)
echo "🔄 Création de Redis pour le cache..."
flyctl redis create \
  --name "nafaplace-redis$DB_SUFFIX" \
  --region cdg \
  --plan free

echo "✅ Création des bases de données terminée pour $ENVIRONMENT"
echo ""
echo "📋 Prochaines étapes:"
echo "1. Attachez les bases de données aux applications:"
echo "   flyctl postgres attach nafaplace-catalog-db$DB_SUFFIX --app nafaplace-catalog-api$DB_SUFFIX"
echo "   flyctl postgres attach nafaplace-identity-db$DB_SUFFIX --app nafaplace-identity-api$DB_SUFFIX"
echo "   flyctl postgres attach nafaplace-order-db$DB_SUFFIX --app nafaplace-order-api$DB_SUFFIX"
echo "   flyctl postgres attach nafaplace-reviews-db$DB_SUFFIX --app nafaplace-reviews-api$DB_SUFFIX"
echo "   flyctl postgres attach nafaplace-notifications-db$DB_SUFFIX --app nafaplace-notifications-api$DB_SUFFIX"
echo "   flyctl redis connect nafaplace-redis$DB_SUFFIX --app nafaplace-cart-api$DB_SUFFIX"
echo ""
echo "2. Exécutez les migrations de base de données"
echo "3. Configurez les secrets avec setup-fly-secrets.sh"
