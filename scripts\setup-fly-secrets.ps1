# Script de configuration des secrets Fly.io pour NafaPlace
# Usage: .\scripts\setup-fly-secrets.ps1

Write-Host "Configuration des secrets Fly.io pour NafaPlace" -ForegroundColor Green

# Ajouter flyctl au PATH pour cette session
$env:PATH += ";C:\Users\<USER>\.fly\bin"

# Vérifier l'authentification
try {
    $whoami = flyctl auth whoami 2>$null
    Write-Host "Connecté en tant que: $whoami" -ForegroundColor Green
} catch {
    Write-Host "Vous n'êtes pas connecté à Fly.io. Connectez-vous avec:" -ForegroundColor Red
    Write-Host "flyctl auth login" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Configuration des chaînes de connexion aux bases de données..." -ForegroundColor Cyan

# Secrets pour Catalog API
Write-Host "Configuration des secrets pour nafaplace-catalog-api..." -ForegroundColor Yellow
flyctl secrets set DATABASE_URL="postgres://postgres:<EMAIL>:5432" --app nafaplace-catalog-api
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-catalog-api
flyctl secrets set JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" --app nafaplace-catalog-api
flyctl secrets set UseCloudinary="true" --app nafaplace-catalog-api
flyctl secrets set Cloudinary__CloudName="your-cloudinary-cloud-name" --app nafaplace-catalog-api
flyctl secrets set Cloudinary__ApiKey="your-cloudinary-api-key" --app nafaplace-catalog-api
flyctl secrets set Cloudinary__ApiSecret="your-cloudinary-api-secret" --app nafaplace-catalog-api

# Secrets pour Identity API
Write-Host "Configuration des secrets pour nafaplace-identity-api..." -ForegroundColor Yellow
flyctl secrets set DATABASE_URL="postgres://postgres:<EMAIL>:5432" --app nafaplace-identity-api
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-identity-api
flyctl secrets set JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" --app nafaplace-identity-api

# Secrets pour Order API
Write-Host "Configuration des secrets pour nafaplace-order-api..." -ForegroundColor Yellow
flyctl secrets set DATABASE_URL="postgres://postgres:<EMAIL>:5432" --app nafaplace-order-api
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-order-api
flyctl secrets set JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" --app nafaplace-order-api

# Secrets pour Reviews API
Write-Host "Configuration des secrets pour nafaplace-reviews-api..." -ForegroundColor Yellow
flyctl secrets set DATABASE_URL="postgres://postgres:<EMAIL>:5432" --app nafaplace-reviews-api
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-reviews-api
flyctl secrets set JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" --app nafaplace-reviews-api

# Secrets pour Notifications API
Write-Host "Configuration des secrets pour nafaplace-notifications-api..." -ForegroundColor Yellow
flyctl secrets set DATABASE_URL="postgres://postgres:<EMAIL>:5432" --app nafaplace-notifications-api
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-notifications-api
flyctl secrets set JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" --app nafaplace-notifications-api

# Secrets pour Cart API (utilise Redis)
Write-Host "Configuration des secrets pour nafaplace-cart-api..." -ForegroundColor Yellow
flyctl secrets set REDIS_URL="redis://default:<EMAIL>:6379" --app nafaplace-cart-api
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-cart-api
flyctl secrets set JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" --app nafaplace-cart-api

# Secrets pour Payment API
Write-Host "Configuration des secrets pour nafaplace-payment-api..." -ForegroundColor Yellow
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-payment-api
flyctl secrets set JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" --app nafaplace-payment-api
flyctl secrets set STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key_here" --app nafaplace-payment-api
flyctl secrets set STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key_here" --app nafaplace-payment-api

# Secrets pour API Gateway
Write-Host "Configuration des secrets pour nafaplace-api-gateway..." -ForegroundColor Yellow
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-api-gateway
flyctl secrets set JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" --app nafaplace-api-gateway

# Secrets pour les applications Web
Write-Host "Configuration des secrets pour les applications web..." -ForegroundColor Yellow

# Web App
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-web
flyctl secrets set API_GATEWAY_URL="https://nafaplace-api-gateway.fly.dev" --app nafaplace-web

# Seller Portal
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-seller-portal
flyctl secrets set API_GATEWAY_URL="https://nafaplace-api-gateway.fly.dev" --app nafaplace-seller-portal

# Admin Portal
flyctl secrets set ASPNETCORE_ENVIRONMENT="Production" --app nafaplace-admin-portal
flyctl secrets set API_GATEWAY_URL="https://nafaplace-api-gateway.fly.dev" --app nafaplace-admin-portal

Write-Host ""
Write-Host "Configuration des secrets terminée !" -ForegroundColor Green
Write-Host ""
Write-Host "IMPORTANT: Changez les secrets suivants avant la production :" -ForegroundColor Red
Write-Host "- JWT_SECRET: Générez une clé secrète forte" -ForegroundColor White
Write-Host "- STRIPE_SECRET_KEY: Votre vraie clé Stripe" -ForegroundColor White
Write-Host "- STRIPE_PUBLISHABLE_KEY: Votre vraie clé publique Stripe" -ForegroundColor White
Write-Host ""
Write-Host "Prochaines étapes :" -ForegroundColor Cyan
Write-Host "1. Vérifiez les secrets : flyctl secrets list --app [app-name]" -ForegroundColor White
Write-Host "2. Déployez via GitHub Actions ou manuellement" -ForegroundColor White
Write-Host "3. Testez les applications" -ForegroundColor White
