name: Deploy to Staging

on:
  push:
    branches: [ develop ]
  pull_request:
    branches: [ main ]

env:
  FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
  REGISTRY: ghcr.io

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  test:
    runs-on: ubuntu-latest
    needs: security-scan
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '9.0.x'
      
      - name: Restore dependencies
        run: dotnet restore
      
      - name: Build
        run: dotnet build --no-restore --configuration Release
      
      - name: Test
        run: dotnet test --no-build --configuration Release --verbosity normal

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [security-scan, test]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - uses: actions/checkout@v4
      
      - uses: superfly/flyctl-actions/setup-flyctl@master
      
      - name: Deploy API Gateway to Staging
        run: |
          flyctl deploy --config fly.toml --app nafaplace-api-gateway-staging
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
      
      - name: Deploy Catalog API to Staging
        run: |
          flyctl deploy --config fly-catalog.toml --app nafaplace-catalog-api-staging
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Deploy Payment API to Staging
        run: |
          # Update Stripe secrets before deployment
          flyctl secrets set STRIPE_SECRET_KEY="${{ secrets.STRIPE_SECRET_KEY }}" --app nafaplace-payment-api-staging
          flyctl secrets set STRIPE_PUBLISHABLE_KEY="${{ secrets.STRIPE_PUBLISHABLE_KEY }}" --app nafaplace-payment-api-staging
          flyctl deploy --config fly-payment.toml --app nafaplace-payment-api-staging
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Deploy Web App to Staging
        run: |
          flyctl deploy --config fly-web.toml --app nafaplace-web-staging
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
      
      - name: Run Health Checks
        run: |
          sleep 30
          curl -f https://nafaplace-api-gateway-staging.fly.dev/health || exit 1
          curl -f https://nafaplace-web-staging.fly.dev/ || exit 1
