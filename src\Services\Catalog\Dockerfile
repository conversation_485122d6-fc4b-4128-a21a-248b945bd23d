# Ultra-simplified Dockerfile for reliable deployment
# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy only the simplified API project
COPY ["src/Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API/NafaPlace.Catalog.API.csproj", "Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API/"]
RUN dotnet restore "Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API/NafaPlace.Catalog.API.csproj"

# Copy only the API source files (no Application/Infrastructure layers)
COPY ["src/Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API/", "Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API/"]
WORKDIR "/src/Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API"
RUN dotnet build "NafaPlace.Catalog.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Catalog.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage - minimal runtime
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Minimal configuration for fast startup
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Dakar

EXPOSE 80
ENTRYPOINT ["dotnet", "NafaPlace.Catalog.API.dll"]
