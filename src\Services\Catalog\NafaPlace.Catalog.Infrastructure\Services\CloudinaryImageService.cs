using CloudinaryDotNet;
using CloudinaryDotNet.Actions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Domain.Models;
using NafaPlace.Catalog.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;

namespace NafaPlace.Catalog.Infrastructure.Services
{
    public class CloudinaryImageService : IProductImageService
    {
        private readonly CatalogDbContext _context;
        private readonly Cloudinary _cloudinary;
        private readonly ILogger<CloudinaryImageService> _logger;

        public CloudinaryImageService(
            CatalogDbContext context,
            IConfiguration configuration,
            ILogger<CloudinaryImageService> logger)
        {
            _context = context;
            _logger = logger;

            // Configuration Cloudinary
            var cloudName = configuration["Cloudinary:CloudName"];
            var apiKey = configuration["Cloudinary:ApiKey"];
            var apiSecret = configuration["Cloudinary:ApiSecret"];

            var account = new Account(cloudName, apiKey, apiSecret);
            _cloudinary = new Cloudinary(account);
        }

        public async Task<string> UploadImageAsync(string base64Image)
        {
            try
            {
                // Convertir base64 en stream
                var imageBytes = Convert.FromBase64String(base64Image);
                
                using var imageStream = new MemoryStream(imageBytes);
                using var optimizedStream = await OptimizeImageAsync(imageStream);

                var uploadParams = new ImageUploadParams()
                {
                    File = new FileDescription($"product_{Guid.NewGuid()}", optimizedStream),
                    Folder = "nafaplace/products",
                    Transformation = new Transformation()
                        .Quality("auto")
                        .FetchFormat("auto")
                        .Width(1920)
                        .Height(1080)
                        .Crop("limit")
                };

                var uploadResult = await _cloudinary.UploadAsync(uploadParams);

                if (uploadResult.Error != null)
                {
                    _logger.LogError($"Erreur upload Cloudinary: {uploadResult.Error.Message}");
                    throw new Exception($"Erreur lors de l'upload: {uploadResult.Error.Message}");
                }

                return uploadResult.SecureUrl.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'upload de l'image vers Cloudinary");
                throw;
            }
        }

        public async Task<ProductImageDto> AddProductImageAsync(int productId, CreateProductImageRequest request)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
            {
                throw new Exception("Le produit est introuvable.");
            }

            // Upload image principale
            var imageUrl = await UploadImageAsync(request.Image);
            
            // Générer URL thumbnail avec Cloudinary
            var thumbnailUrl = GenerateCloudinaryThumbnail(imageUrl);

            var productImage = new ProductImage
            {
                ProductId = productId,
                ImageUrl = imageUrl,
                ThumbnailUrl = thumbnailUrl,
                IsMain = request.IsMain
            };

            if (request.IsMain)
            {
                var existingMainImages = await _context.ProductImages
                    .Where(pi => pi.ProductId == productId && pi.IsMain)
                    .ToListAsync();

                foreach (var img in existingMainImages)
                {
                    img.IsMain = false;
                }
            }

            _context.ProductImages.Add(productImage);
            await _context.SaveChangesAsync();

            return new ProductImageDto
            {
                Id = productImage.Id,
                ProductId = productImage.ProductId,
                Url = productImage.ImageUrl ?? string.Empty,
                ThumbnailUrl = productImage.ThumbnailUrl,
                IsMain = productImage.IsMain
            };
        }

        public async Task<IEnumerable<ProductImageDto>> AddBulkProductImagesAsync(int productId, IEnumerable<CreateProductImageRequest> requests)
        {
            var results = new List<ProductImageDto>();

            foreach (var request in requests)
            {
                var result = await AddProductImageAsync(productId, request);
                results.Add(result);
            }

            return results;
        }

        public async Task DeleteProductImageAsync(int imageId)
        {
            var productImage = await _context.ProductImages.FindAsync(imageId);
            if (productImage == null)
            {
                throw new Exception("Image introuvable.");
            }

            // Supprimer de Cloudinary
            await DeleteFromCloudinaryAsync(productImage.ImageUrl);

            _context.ProductImages.Remove(productImage);
            await _context.SaveChangesAsync();
        }

        public async Task SetMainImageAsync(int productId, int imageId)
        {
            var existingMainImages = await _context.ProductImages
                .Where(pi => pi.ProductId == productId && pi.IsMain)
                .ToListAsync();

            foreach (var img in existingMainImages)
            {
                img.IsMain = false;
            }

            var newMainImage = await _context.ProductImages.FindAsync(imageId);
            if (newMainImage != null && newMainImage.ProductId == productId)
            {
                newMainImage.IsMain = true;
            }

            await _context.SaveChangesAsync();
        }

        public Task<bool> ValidateImageAsync(string image)
        {
            try
            {
                var imageBytes = Convert.FromBase64String(image);
                return Task.FromResult(imageBytes.Length > 0 && imageBytes.Length <= 10 * 1024 * 1024); // Max 10MB
            }
            catch
            {
                return Task.FromResult(false);
            }
        }

        public async Task<ProductImageDto> GetProductImageAsync(int imageId)
        {
            var productImage = await _context.ProductImages.FindAsync(imageId);
            if (productImage == null)
            {
                throw new Exception("Image introuvable.");
            }

            return new ProductImageDto
            {
                Id = productImage.Id,
                ProductId = productImage.ProductId,
                Url = productImage.ImageUrl ?? string.Empty,
                ThumbnailUrl = productImage.ThumbnailUrl,
                IsMain = productImage.IsMain
            };
        }

        private async Task<MemoryStream> OptimizeImageAsync(Stream imageStream)
        {
            var outputStream = new MemoryStream();
            
            using var image = await Image.LoadAsync(imageStream);
            
            // Redimensionner si nécessaire
            if (image.Width > 1920 || image.Height > 1080)
            {
                image.Mutate(x => x.Resize(new ResizeOptions
                {
                    Size = new SixLabors.ImageSharp.Size(1920, 1080),
                    Mode = ResizeMode.Max
                }));
            }

            // Sauvegarder avec compression JPEG
            await image.SaveAsJpegAsync(outputStream, new JpegEncoder
            {
                Quality = 85
            });

            outputStream.Position = 0;
            return outputStream;
        }

        private string GenerateCloudinaryThumbnail(string imageUrl)
        {
            // Cloudinary permet de générer des thumbnails à la volée
            // Remplacer /upload/ par /upload/w_300,h_300,c_fill/
            return imageUrl.Replace("/upload/", "/upload/w_300,h_300,c_fill/");
        }

        private async Task DeleteFromCloudinaryAsync(string imageUrl)
        {
            try
            {
                // Extraire le public_id de l'URL Cloudinary
                var uri = new Uri(imageUrl);
                var pathSegments = uri.AbsolutePath.Split('/');
                var publicIdWithExtension = pathSegments.Last();
                var publicId = Path.GetFileNameWithoutExtension(publicIdWithExtension);
                
                // Inclure le dossier dans le public_id
                var folderIndex = Array.IndexOf(pathSegments, "nafaplace");
                if (folderIndex >= 0 && folderIndex < pathSegments.Length - 1)
                {
                    var folderPath = string.Join("/", pathSegments.Skip(folderIndex).Take(pathSegments.Length - folderIndex - 1));
                    publicId = $"{folderPath}/{publicId}";
                }

                var deleteParams = new DeletionParams(publicId);
                await _cloudinary.DestroyAsync(deleteParams);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Impossible de supprimer l'image de Cloudinary: {imageUrl}");
            }
        }
    }
}
