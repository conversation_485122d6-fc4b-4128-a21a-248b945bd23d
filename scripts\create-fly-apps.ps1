# Script de création des applications Fly.io pour NafaPlace
# Usage: .\scripts\create-fly-apps.ps1

Write-Host "🚀 Création des applications Fly.io pour NafaPlace" -ForegroundColor Green

# Ajouter flyctl au PATH pour cette session
$env:PATH += ";C:\Users\<USER>\.fly\bin"

# Vérifier l'authentification
try {
    $whoami = flyctl auth whoami 2>$null
    Write-Host "✅ Connecté en tant que: $whoami" -ForegroundColor Green
} catch {
    Write-Host "❌ Vous n'êtes pas connecté à Fly.io. Connectez-vous avec:" -ForegroundColor Red
    Write-Host "flyctl auth login" -ForegroundColor Yellow
    exit 1
}

# Applications Production
Write-Host ""
Write-Host "📦 Création des applications PRODUCTION..." -ForegroundColor Cyan

$apps_production = @(
    "nafaplace-api-gateway",
    "nafaplace-catalog-api",
    "nafaplace-identity-api",
    "nafaplace-cart-api",
    "nafaplace-order-api",
    "nafaplace-payment-api",
    "nafaplace-reviews-api",
    "nafaplace-notifications-api",
    "nafaplace-web",
    "nafaplace-seller-portal",
    "nafaplace-admin-portal"
)

foreach ($app in $apps_production) {
    Write-Host "🔨 Création de $app..." -ForegroundColor Yellow
    $result = flyctl apps create $app --org personal 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ $app créé avec succès" -ForegroundColor Green
    } else {
        Write-Host "⚠️  $app existe déjà ou erreur lors de la création" -ForegroundColor DarkYellow
    }
}

# Applications Staging
Write-Host ""
Write-Host "🧪 Création des applications STAGING..." -ForegroundColor Cyan

$apps_staging = @(
    "nafaplace-api-gateway-staging",
    "nafaplace-catalog-api-staging",
    "nafaplace-identity-api-staging",
    "nafaplace-cart-api-staging",
    "nafaplace-order-api-staging",
    "nafaplace-payment-api-staging",
    "nafaplace-reviews-api-staging",
    "nafaplace-notifications-api-staging",
    "nafaplace-web-staging",
    "nafaplace-seller-portal-staging",
    "nafaplace-admin-portal-staging"
)

foreach ($app in $apps_staging) {
    Write-Host "🔨 Création de $app..." -ForegroundColor Yellow
    $result = flyctl apps create $app --org personal 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ $app créé avec succès" -ForegroundColor Green
    } else {
        Write-Host "⚠️  $app existe déjà ou erreur lors de la création" -ForegroundColor DarkYellow
    }
}

Write-Host ""
Write-Host "✅ Toutes les applications ont été créées !" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Prochaines étapes :" -ForegroundColor Cyan
Write-Host "1. Créer les bases de données : .\scripts\setup-fly-databases.ps1" -ForegroundColor White
Write-Host "2. Configurer les secrets : .\scripts\setup-fly-secrets.ps1" -ForegroundColor White
Write-Host "3. Configurer GitHub Actions avec FLY_API_TOKEN" -ForegroundColor White
Write-Host "4. Déployer via GitHub Actions ou manuellement" -ForegroundColor White
Write-Host ""
Write-Host "🔍 Vérifiez vos applications :" -ForegroundColor Cyan
Write-Host "flyctl apps list" -ForegroundColor White
