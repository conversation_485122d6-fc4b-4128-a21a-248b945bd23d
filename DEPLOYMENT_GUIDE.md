# 🚀 Guide de Déploiement NafaPlace sur Fly.io

## 📋 Prérequis

### 1. Comptes et Outils
- [ ] Compte Fly.io créé
- [ ] CLI Fly.io installé (`curl -L https://fly.io/install.sh | sh`)
- [ ] Compte GitHub avec accès au repository
- [ ] Compte Stripe pour les paiements

### 2. Variables d'Environnement Requises
```bash
# Stripe (Production)
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Stockage Azure/AWS
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;...

# Autres services (optionnel)
SENDGRID_API_KEY=SG...
TWILIO_AUTH_TOKEN=...
```

## 🔧 Configuration Initiale

### 1. Authentification Fly.io
```bash
flyctl auth login
```

### 2. Création des Applications
```bash
# Production
flyctl apps create nafaplace-api-gateway
flyctl apps create nafaplace-catalog-api
flyctl apps create nafaplace-identity-api
flyctl apps create nafaplace-cart-api
flyctl apps create nafaplace-order-api
flyctl apps create nafaplace-payment-api
flyctl apps create nafaplace-reviews-api
flyctl apps create nafaplace-notifications-api
flyctl apps create nafaplace-web
flyctl apps create nafaplace-seller-portal
flyctl apps create nafaplace-admin-portal

# Staging
flyctl apps create nafaplace-api-gateway-staging
flyctl apps create nafaplace-catalog-api-staging
flyctl apps create nafaplace-identity-api-staging
flyctl apps create nafaplace-cart-api-staging
flyctl apps create nafaplace-order-api-staging
flyctl apps create nafaplace-payment-api-staging
flyctl apps create nafaplace-reviews-api-staging
flyctl apps create nafaplace-notifications-api-staging
flyctl apps create nafaplace-web-staging
flyctl apps create nafaplace-seller-portal-staging
flyctl apps create nafaplace-admin-portal-staging
```

### 3. Configuration des Bases de Données
```bash
# Exécuter le script de création des BDD
chmod +x scripts/setup-fly-databases.sh
./scripts/setup-fly-databases.sh staging
./scripts/setup-fly-databases.sh production
```

### 4. Configuration des Secrets
```bash
# Définir les variables d'environnement
export STRIPE_SECRET_KEY="sk_live_..."
export AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;..."

# Exécuter le script de configuration des secrets
chmod +x scripts/setup-fly-secrets.sh
./scripts/setup-fly-secrets.sh staging
./scripts/setup-fly-secrets.sh production
```

## 🔐 Configuration GitHub Actions

### 1. Secrets GitHub à Configurer
Dans GitHub Repository → Settings → Secrets and variables → Actions :

```
FLY_API_TOKEN=fly_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;...
```

### 2. Environnements GitHub
Créer les environnements dans GitHub :
- `staging` : Protection avec review requis
- `production` : Protection avec review requis + délai

## 🚀 Processus de Déploiement

### 1. Déploiement Staging
- Push sur la branche `develop`
- GitHub Actions déploie automatiquement sur staging
- Tests automatiques et vérifications de sécurité

### 2. Déploiement Production
- Push sur la branche `main`
- Review requis avant déploiement
- Déploiement automatique après approbation

## 🔍 Monitoring et Logs

### 1. Vérification des Applications
```bash
flyctl status --app nafaplace-api-gateway
flyctl logs --app nafaplace-api-gateway
```

### 2. Health Checks
- API Gateway : https://nafaplace-api-gateway.fly.dev/health
- Web App : https://nafaplace-web.fly.dev/

### 3. Métriques
```bash
flyctl metrics --app nafaplace-api-gateway
```

## 🛠️ Maintenance

### 1. Mise à Jour des Secrets
```bash
flyctl secrets set SECRET_NAME=new_value --app app-name
```

### 2. Scaling
```bash
flyctl scale count 2 --app nafaplace-api-gateway
flyctl scale memory 2048 --app nafaplace-api-gateway
```

### 3. Backup Base de Données
```bash
flyctl postgres backup list --app nafaplace-catalog-db
```

## 🚨 Sécurité

### 1. Bonnes Pratiques Appliquées
- ✅ Secrets chiffrés dans GitHub
- ✅ Variables d'environnement sécurisées
- ✅ Scan de vulnérabilités automatique
- ✅ HTTPS forcé
- ✅ Environnements séparés
- ✅ Review requis pour production

### 2. Monitoring de Sécurité
- Trivy scan à chaque déploiement
- Dependabot pour les dépendances
- Logs centralisés sur Fly.io

## 📞 Support

En cas de problème :
1. Vérifiez les logs : `flyctl logs --app app-name`
2. Vérifiez le statut : `flyctl status --app app-name`
3. Consultez la documentation Fly.io
4. Contactez le support Fly.io si nécessaire
