# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Services/Payment/NafaPlace.Payment.API/NafaPlace.Payment.API.csproj", "Services/Payment/NafaPlace.Payment.API/"]
RUN dotnet restore "Services/Payment/NafaPlace.Payment.API/NafaPlace.Payment.API.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Services/Payment/NafaPlace.Payment.API/", "Services/Payment/NafaPlace.Payment.API/"]
WORKDIR "/src/Services/Payment/NafaPlace.Payment.API"
RUN dotnet build "NafaPlace.Payment.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Payment.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Minimal configuration for fast startup
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Dakar

EXPOSE 80
ENTRYPOINT ["dotnet", "NafaPlace.Payment.API.dll"]