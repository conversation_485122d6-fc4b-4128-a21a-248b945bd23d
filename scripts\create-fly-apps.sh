#!/bin/bash

# Script de création des applications Fly.io pour NafaPlace
# Usage: ./create-fly-apps.sh

echo "🚀 Création des applications Fly.io pour NafaPlace"

# Vérifier que flyctl est installé
if ! command -v flyctl &> /dev/null; then
    echo "❌ flyctl n'est pas installé. Installez-le avec:"
    echo "curl -L https://fly.io/install.sh | sh"
    exit 1
fi

# Vérifier l'authentification
if ! flyctl auth whoami &> /dev/null; then
    echo "❌ Vous n'êtes pas connecté à Fly.io. Connectez-vous avec:"
    echo "flyctl auth login"
    exit 1
fi

echo "✅ flyctl installé et authentifié"

# Applications Production
echo ""
echo "📦 Création des applications PRODUCTION..."

apps_production=(
    "nafaplace-api-gateway"
    "nafaplace-catalog-api"
    "nafaplace-identity-api"
    "nafaplace-cart-api"
    "nafaplace-order-api"
    "nafaplace-payment-api"
    "nafaplace-reviews-api"
    "nafaplace-notifications-api"
    "nafaplace-web"
    "nafaplace-seller-portal"
    "nafaplace-admin-portal"
)

for app in "${apps_production[@]}"; do
    echo "🔨 Création de $app..."
    flyctl apps create "$app" --org personal 2>/dev/null || echo "⚠️  $app existe déjà"
done

# Applications Staging
echo ""
echo "🧪 Création des applications STAGING..."

apps_staging=(
    "nafaplace-api-gateway-staging"
    "nafaplace-catalog-api-staging"
    "nafaplace-identity-api-staging"
    "nafaplace-cart-api-staging"
    "nafaplace-order-api-staging"
    "nafaplace-payment-api-staging"
    "nafaplace-reviews-api-staging"
    "nafaplace-notifications-api-staging"
    "nafaplace-web-staging"
    "nafaplace-seller-portal-staging"
    "nafaplace-admin-portal-staging"
)

for app in "${apps_staging[@]}"; do
    echo "🔨 Création de $app..."
    flyctl apps create "$app" --org personal 2>/dev/null || echo "⚠️  $app existe déjà"
done

echo ""
echo "✅ Toutes les applications ont été créées !"
echo ""
echo "📋 Prochaines étapes :"
echo "1. Créer les bases de données : ./scripts/setup-fly-databases.sh"
echo "2. Configurer les secrets : ./scripts/setup-fly-secrets.sh"
echo "3. Configurer GitHub Actions avec FLY_API_TOKEN"
echo "4. Déployer via GitHub Actions ou manuellement"
echo ""
echo "🔍 Vérifiez vos applications :"
echo "flyctl apps list"
