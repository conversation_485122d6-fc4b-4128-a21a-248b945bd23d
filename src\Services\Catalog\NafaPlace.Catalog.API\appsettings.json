{"ConnectionStrings": {"DefaultConnection": "Host=catalog-db;Port=5432;Database=NafaPlace.Catalog;Username=postgres;Password=*****************;Integrated Security=True", "AzureStorage": "UseDevelopmentStorage=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Storage": {"ProductImagesContainer": "product-images", "CategoryImagesContainer": "category-images"}, "UseCloudinary": true, "Cloudinary": {"CloudName": "your-cloud-name", "ApiKey": "your-api-key", "ApiSecret": "your-api-secret"}}