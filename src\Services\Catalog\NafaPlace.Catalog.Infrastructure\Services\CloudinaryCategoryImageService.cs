using CloudinaryDotNet;
using CloudinaryDotNet.Actions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Application.Common.Interfaces;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;

namespace NafaPlace.Catalog.Infrastructure.Services
{
    public class CloudinaryCategoryImageService : ICategoryImageService
    {
        private readonly Cloudinary _cloudinary;
        private readonly ILogger<CloudinaryCategoryImageService> _logger;

        public CloudinaryCategoryImageService(
            IConfiguration configuration,
            ILogger<CloudinaryCategoryImageService> logger)
        {
            _logger = logger;

            // Configuration Cloudinary
            var cloudName = configuration["Cloudinary:CloudName"];
            var apiKey = configuration["Cloudinary:ApiKey"];
            var apiSecret = configuration["Cloudinary:ApiSecret"];

            var account = new Account(cloudName, apiKey, apiSecret);
            _cloudinary = new Cloudinary(account);
        }

        public async Task<string> UploadCategoryImageAsync(string base64Image)
        {
            try
            {
                // Convertir base64 en stream
                var imageBytes = Convert.FromBase64String(base64Image);
                
                using var imageStream = new MemoryStream(imageBytes);
                using var optimizedStream = await OptimizeImageAsync(imageStream);

                var uploadParams = new ImageUploadParams()
                {
                    File = new FileDescription($"category_{Guid.NewGuid()}", optimizedStream),
                    Folder = "nafaplace/categories",
                    Transformation = new Transformation()
                        .Quality("auto")
                        .FetchFormat("auto")
                        .Width(800)
                        .Height(600)
                        .Crop("fill")
                };

                var uploadResult = await _cloudinary.UploadAsync(uploadParams);

                if (uploadResult.Error != null)
                {
                    _logger.LogError($"Erreur upload Cloudinary: {uploadResult.Error.Message}");
                    throw new Exception($"Erreur lors de l'upload: {uploadResult.Error.Message}");
                }

                return uploadResult.SecureUrl.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'upload de l'image de catégorie vers Cloudinary");
                throw;
            }
        }

        public Task<string> GenerateThumbnailAsync(string imageUrl)
        {
            // Cloudinary permet de générer des thumbnails à la volée
            // Pas besoin de stocker séparément, on peut transformer l'URL
            return Task.FromResult(imageUrl.Replace("/upload/", "/upload/w_200,h_200,c_fill/"));
        }

        public Task<bool> ValidateImageAsync(string image)
        {
            try
            {
                var imageBytes = Convert.FromBase64String(image);
                return Task.FromResult(imageBytes.Length > 0 && imageBytes.Length <= 5 * 1024 * 1024); // Max 5MB pour les catégories
            }
            catch
            {
                return Task.FromResult(false);
            }
        }

        public async Task DeleteImageAsync(string imageUrl)
        {
            try
            {
                // Extraire le public_id de l'URL Cloudinary
                var uri = new Uri(imageUrl);
                var pathSegments = uri.AbsolutePath.Split('/');
                var publicIdWithExtension = pathSegments.Last();
                var publicId = Path.GetFileNameWithoutExtension(publicIdWithExtension);
                
                // Inclure le dossier dans le public_id
                var folderIndex = Array.IndexOf(pathSegments, "nafaplace");
                if (folderIndex >= 0 && folderIndex < pathSegments.Length - 1)
                {
                    var folderPath = string.Join("/", pathSegments.Skip(folderIndex).Take(pathSegments.Length - folderIndex - 1));
                    publicId = $"{folderPath}/{publicId}";
                }

                var deleteParams = new DeletionParams(publicId);
                await _cloudinary.DestroyAsync(deleteParams);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Impossible de supprimer l'image de catégorie de Cloudinary: {imageUrl}");
            }
        }

        private async Task<MemoryStream> OptimizeImageAsync(Stream imageStream)
        {
            var outputStream = new MemoryStream();
            
            using var image = await Image.LoadAsync(imageStream);
            
            // Redimensionner pour les catégories (plus petit que les produits)
            if (image.Width > 800 || image.Height > 600)
            {
                image.Mutate(x => x.Resize(new ResizeOptions
                {
                    Size = new SixLabors.ImageSharp.Size(800, 600),
                    Mode = ResizeMode.Max
                }));
            }

            // Sauvegarder avec compression JPEG
            await image.SaveAsJpegAsync(outputStream, new JpegEncoder
            {
                Quality = 90 // Qualité légèrement supérieure pour les catégories
            });

            outputStream.Position = 0;
            return outputStream;
        }
    }
}
