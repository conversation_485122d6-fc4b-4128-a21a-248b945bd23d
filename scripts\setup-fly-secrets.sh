#!/bin/bash

# Script de configuration des secrets Fly.io pour NafaPlace
# Usage: ./setup-fly-secrets.sh [staging|production]

ENVIRONMENT=${1:-staging}

if [ "$ENVIRONMENT" = "staging" ]; then
    APP_SUFFIX="-staging"
else
    APP_SUFFIX=""
fi

echo "🔐 Configuration des secrets pour l'environnement: $ENVIRONMENT"

# Secrets pour l'API Gateway
echo "📡 Configuration API Gateway..."
flyctl secrets set \
  --app "nafaplace-api-gateway$APP_SUFFIX" \
  JWT_SECRET_KEY="$(openssl rand -base64 32)" \
  ASPNETCORE_ENVIRONMENT="Production"

# Secrets pour le service Catalog
echo "📦 Configuration Catalog API..."
flyctl secrets set \
  --app "nafaplace-catalog-api$APP_SUFFIX" \
  AZURE_STORAGE_CONNECTION_STRING="$AZURE_STORAGE_CONNECTION_STRING" \
  JWT_SECRET_KEY="$(openssl rand -base64 32)"

# Secrets pour le service Identity
echo "🔑 Configuration Identity API..."
flyctl secrets set \
  --app "nafaplace-identity-api$APP_SUFFIX" \
  JWT_SECRET_KEY="$(openssl rand -base64 32)" \
  IDENTITY_ENCRYPTION_KEY="$(openssl rand -base64 32)"

# Secrets pour le service Payment
echo "💳 Configuration Payment API..."
flyctl secrets set \
  --app "nafaplace-payment-api$APP_SUFFIX" \
  STRIPE_SECRET_KEY="$STRIPE_SECRET_KEY" \
  STRIPE_WEBHOOK_SECRET="$STRIPE_WEBHOOK_SECRET" \
  JWT_SECRET_KEY="$(openssl rand -base64 32)"

# Secrets pour le service Cart
echo "🛒 Configuration Cart API..."
flyctl secrets set \
  --app "nafaplace-cart-api$APP_SUFFIX" \
  JWT_SECRET_KEY="$(openssl rand -base64 32)"

# Secrets pour le service Order
echo "📋 Configuration Order API..."
flyctl secrets set \
  --app "nafaplace-order-api$APP_SUFFIX" \
  JWT_SECRET_KEY="$(openssl rand -base64 32)"

# Secrets pour le service Reviews
echo "⭐ Configuration Reviews API..."
flyctl secrets set \
  --app "nafaplace-reviews-api$APP_SUFFIX" \
  JWT_SECRET_KEY="$(openssl rand -base64 32)"

# Secrets pour le service Notifications
echo "🔔 Configuration Notifications API..."
flyctl secrets set \
  --app "nafaplace-notifications-api$APP_SUFFIX" \
  JWT_SECRET_KEY="$(openssl rand -base64 32)"

# Secrets pour l'application Web
echo "🌐 Configuration Web App..."
flyctl secrets set \
  --app "nafaplace-web$APP_SUFFIX" \
  JWT_SECRET_KEY="$(openssl rand -base64 32)"

echo "✅ Configuration des secrets terminée pour $ENVIRONMENT"
echo ""
echo "📋 Prochaines étapes:"
echo "1. Vérifiez les secrets: flyctl secrets list --app nafaplace-api-gateway$APP_SUFFIX"
echo "2. Déployez les applications: flyctl deploy"
echo "3. Vérifiez les logs: flyctl logs --app nafaplace-api-gateway$APP_SUFFIX"
