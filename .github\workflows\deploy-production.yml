name: Deploy to Production

on:
  push:
    branches: [ main ]
  release:
    types: [ published ]

env:
  FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

jobs:
  security-scan:
    runs-on: ubuntu-latest
    continue-on-error: true
    steps:
      - uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        continue-on-error: true
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        continue-on-error: true
        with:
          sarif_file: 'trivy-results.sarif'

  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '9.0.x'
      
      - name: Restore dependencies
        run: dotnet restore
      
      - name: Build
        run: dotnet build --no-restore --configuration Release
      
      - name: Test
        run: dotnet test --no-build --configuration Release --verbosity normal

  deploy-production:
    runs-on: ubuntu-latest
    needs: [security-scan, test]
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - uses: superfly/flyctl-actions/setup-flyctl@master
      
      - name: Deploy API Gateway to Production
        run: |
          flyctl deploy --config fly.toml --app nafaplace-api-gateway
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
      
      - name: Deploy Catalog API to Production
        run: |
          # Update Cloudinary secrets before deployment
          flyctl secrets set Cloudinary__CloudName="${{ secrets.CLOUDINARY_CLOUD_NAME }}" --app nafaplace-catalog-api
          flyctl secrets set Cloudinary__ApiKey="${{ secrets.CLOUDINARY_API_KEY }}" --app nafaplace-catalog-api
          flyctl secrets set Cloudinary__ApiSecret="${{ secrets.CLOUDINARY_API_SECRET }}" --app nafaplace-catalog-api
          flyctl deploy --config fly-catalog.toml --app nafaplace-catalog-api
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Deploy Payment API to Production
        run: |
          # Update Stripe secrets before deployment
          flyctl secrets set STRIPE_SECRET_KEY="${{ secrets.STRIPE_SECRET_KEY }}" --app nafaplace-payment-api
          flyctl secrets set STRIPE_PUBLISHABLE_KEY="${{ secrets.STRIPE_PUBLISHABLE_KEY }}" --app nafaplace-payment-api
          flyctl deploy --config fly-payment.toml --app nafaplace-payment-api
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Deploy Web App to Production
        run: |
          flyctl deploy --config fly-web.toml --app nafaplace-web
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
      
      - name: Run Health Checks
        run: |
          sleep 30
          curl -f https://nafaplace-api-gateway.fly.dev/health || exit 1
          curl -f https://nafaplace-web.fly.dev/ || exit 1
      
      - name: Notify Deployment Success
        if: success()
        run: |
          echo "✅ Production deployment successful!"
          echo "🌐 Web App: https://nafaplace-web.fly.dev"
          echo "🔗 API Gateway: https://nafaplace-api-gateway.fly.dev"
